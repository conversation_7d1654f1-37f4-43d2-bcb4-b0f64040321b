//! MATRIX IDE Application
//!
//! Implementazione dell'applicazione principale di MATRIX IDE
//! utilizzando i componenti ottimizzati e le API di Floem 0.2.

use std::sync::Arc;
use std::path::PathBuf;

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, Decorators},
    View,
};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
    layout_improved::MatrixLayout,
    lapce_bridge_improved::LapceIntegration,
    components::{
        title_bar_improved::TitleBar,
        file_explorer_improved::FileExplorer,
    },
};
use matrix_core::Engine as CoreEngine;

/// Evento dell'applicazione
#[derive(Debug, Clone)]
pub enum AppEvent {
    /// Apri file
    OpenFile(PathBuf),

    /// Crea nuovo file
    NewFile,

    /// Salva file
    SaveFile,

    /// Salva file con nome
    SaveFileAs(PathBuf),

    /// Apri directory progetto
    OpenProject(PathBuf),

    /// Cambia tema
    ChangeTheme(String),

    /// Esci dall'applicazione
    Exit,
}

/// Configurazione dell'applicazione
#[derive(Debug, Clone)]
pub struct AppConfig {
    /// Titolo finestra
    pub window_title: String,

    /// Dimensione iniziale finestra
    pub window_size: (u32, u32),

    /// Tema iniziale
    pub initial_theme: String,

    /// Directory log
    pub log_directory: PathBuf,

    /// Debug mode
    pub debug_mode: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            window_title: "MATRIX IDE".to_string(),
            window_size: (1280, 800),
            initial_theme: "dark".to_string(),
            log_directory: PathBuf::from("./logs"),
            debug_mode: false,
        }
    }
}

/// Applicazione MATRIX IDE
pub struct App {
    /// Configurazione
    config: AppConfig,

    /// Core engine
    core: Arc<CoreEngine>,

    /// Theme manager
    theme_manager: Arc<ThemeManager>,

    /// Lapce integration
    lapce: Arc<LapceIntegration>,

    /// Layout principale
    layout: Arc<MatrixLayout>,

    /// Directory di progetto corrente
    project_directory: RwSignal<Option<PathBuf>>,

    /// Canale eventi
    event_sender: RwSignal<Option<tokio::sync::mpsc::Sender<AppEvent>>>,

    /// Finestra principale
    main_window: RwSignal<Option<String>>, // Placeholder per window handle

    /// Flag inizializzazione completata
    initialized: RwSignal<bool>,
}

impl App {
    /// Crea una nuova istanza dell'applicazione
    pub fn new() -> Result<Self, UiError> {
        // Crea config di default
        let config = AppConfig::default();

        // Inizializza il core engine
        let core = Arc::new(CoreEngine::new()?);

        // Inizializza il theme manager
        let theme_manager = Arc::new(ThemeManager::new()?);

        // Crea Lapce integration
        let lapce = Arc::new(LapceIntegration::new(core.clone(), theme_manager.clone())?);

        // Crea layout
        let layout = Arc::new(MatrixLayout::new(theme_manager.clone(), lapce.clone())?);

        // Crea app
        Ok(Self {
            config,
            core,
            theme_manager,
            lapce,
            layout,
            project_directory: create_rw_signal(None),
            event_sender: create_rw_signal(None),
            main_window: create_rw_signal(None),
            initialized: create_rw_signal(false),
        })
    }

    /// Crea una nuova istanza dell'applicazione con configurazione personalizzata
    pub fn with_config(config: AppConfig) -> Result<Self, UiError> {
        let mut app = App::new()?;
        app.config = config;
        Ok(app)
    }

    /// Inizializza l'applicazione
    pub async fn initialize(&mut self) -> Result<(), UiError> {
        if self.initialized.get() {
            return Ok(());
        }

        // Inizializza il tema
        self.theme_manager.set_active_theme(&self.config.initial_theme)?;

        // Inizializza l'integrazione Lapce
        self.lapce.initialize()?;

        // Crea canale eventi
        let (tx, mut rx) = tokio::sync::mpsc::channel::<AppEvent>(100);
        self.event_sender.set(Some(tx));

        // Clona riferimenti per il task di gestione eventi
        let core = self.core.clone();
        let theme_manager = self.theme_manager.clone();
        let lapce = self.lapce.clone();
        let project_directory = self.project_directory;

        // Avvia task di gestione eventi
        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                match event {
                    AppEvent::OpenFile(path) => {
                        if let Err(e) = lapce.open_file(path) {
                            eprintln!("Errore nell'apertura del file: {}", e);
                        }
                    },
                    AppEvent::NewFile => {
                        if let Err(e) = lapce.create_new_file() {
                            eprintln!("Errore nella creazione del nuovo file: {}", e);
                        }
                    },
                    AppEvent::SaveFile => {
                        if let Err(e) = lapce.save_current_file() {
                            eprintln!("Errore nel salvataggio del file: {}", e);
                        }
                    },
                    AppEvent::SaveFileAs(path) => {
                        if let Err(e) = lapce.save_current_file_as(&path) {
                            eprintln!("Errore nel salvataggio del file: {}", e);
                        }
                    },
                    AppEvent::OpenProject(path) => {
                        project_directory.set(Some(path));
                    },
                    AppEvent::ChangeTheme(theme_name) => {
                        if let Err(e) = theme_manager.set_active_theme(&theme_name) {
                            eprintln!("Errore nel cambio tema: {}", e);
                        }
                    },
                    AppEvent::Exit => {
                        // Gestione uscita, salvare stato, ecc.
                        println!("Uscita in corso...");
                    },
                }
            }
        });

        self.initialized.set(true);

        Ok(())
    }

    /// Imposta la directory del progetto
    pub fn set_project_directory(&self, path: PathBuf) -> Result<(), UiError> {
        if !path.exists() || !path.is_dir() {
            return Err(UiError::InvalidPath(path.display().to_string()));
        }

        self.project_directory.set(Some(path.clone()));

        // Aggiorna file explorer
        // Note: This method doesn't exist in the current layout, so we'll skip it
        // let file_explorer = self.layout.get_file_explorer();
        // file_explorer.set_root_directory(path)?;

        Ok(())
    }

    /// Invia un evento all'applicazione
    pub fn send_event(&self, event: AppEvent) -> Result<(), UiError> {
        if let Some(tx) = &self.event_sender.get() {
            tx.try_send(event).map_err(|e| UiError::EventError(e.to_string()))?;
        }

        Ok(())
    }

    /// Esegue l'applicazione
    pub fn run(&self) -> Result<(), UiError> {
        if !self.initialized.get() {
            return Err(UiError::NotInitialized("App not initialized".to_string()));
        }

        let app_view = self.create_app_view()?;
        floem::launch(move || app_view);
        Ok(())
    }

    /// Crea la vista principale dell'applicazione
    fn create_app_view(&self) -> Result<impl View, UiError> {
        let layout = self.layout.clone();

        layout.build()
    }

    /// Termina l'applicazione
    pub fn shutdown(&self) -> Result<(), UiError> {
        // Invia evento di uscita
        self.send_event(AppEvent::Exit)?;

        // Chiudi finestra principale
        // Floem 0.2 does not expose a direct way to close a window by its handle from outside the application loop.
        // The application will exit when all windows are closed or the application loop finishes.
        // For now, we'll just clear the main_window signal.
        self.main_window.set(None);

        Ok(())
    }
}
