//! Output Panel per MATRIX IDE
//!
//! Pannello per visualizzare l'output di build, test e altri processi.

use std::sync::Arc;
use std::collections::VecDeque;

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, label, v_stack, scroll, Decorators},
    style::{AlignItems, JustifyContent},
    text::Weight,
    View,
};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
};

use matrix_core::Engine as CoreEngine;

/// Tipo di messaggio di output
#[derive(Debug, Clone, PartialEq)]
pub enum OutputType {
    Info,
    Warning,
    Error,
    Success,
    Debug,
    Build,
    Test,
    Command,
}

/// Messaggio di output
#[derive(Debug, Clone)]
pub struct OutputMessage {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message_type: OutputType,
    pub content: String,
    pub source: Option<String>,
    pub file_path: Option<String>,
    pub line_number: Option<u32>,
}

/// Pannello di output
pub struct OutputPanel {
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    messages: RwSignal<VecDeque<OutputMessage>>,
    filter_type: RwSignal<Option<OutputType>>,
    auto_scroll: RwSignal<bool>,
    max_messages: usize,
    search_query: RwSignal<String>,
}

impl OutputPanel {
    /// Crea un nuovo pannello di output
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            core,
            theme_manager,
            messages: create_rw_signal(VecDeque::new()),
            filter_type: create_rw_signal(None),
            auto_scroll: create_rw_signal(true),
            max_messages: 1000,
            search_query: create_rw_signal(String::new()),
        })
    }

    /// Aggiunge un messaggio di output
    pub fn add_message(&self, message: OutputMessage) {
        self.messages.update(|messages| {
            messages.push_back(message);
            
            // Mantieni solo gli ultimi max_messages messaggi
            while messages.len() > self.max_messages {
                messages.pop_front();
            }
        });
    }

    /// Aggiunge un messaggio semplice
    pub fn add_simple_message(&self, message_type: OutputType, content: String) {
        let message = OutputMessage {
            timestamp: chrono::Utc::now(),
            message_type,
            content,
            source: None,
            file_path: None,
            line_number: None,
        };
        self.add_message(message);
    }

    /// Pulisce tutti i messaggi
    pub fn clear(&self) {
        self.messages.set(VecDeque::new());
    }

    /// Imposta il filtro per tipo di messaggio
    pub fn set_filter(&self, filter: Option<OutputType>) {
        self.filter_type.set(filter);
    }

    /// Imposta la query di ricerca
    pub fn set_search_query(&self, query: String) {
        self.search_query.set(query);
    }

    /// Toggle auto-scroll
    pub fn toggle_auto_scroll(&self) {
        self.auto_scroll.update(|auto| *auto = !*auto);
    }

    /// Crea la vista del pannello (alias per compatibilità)
    pub fn build(&self) -> impl View {
        self.create_view()
    }

    /// Crea la vista del pannello
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let messages = self.messages;
        let filter_type = self.filter_type;
        let search_query = self.search_query;

        container(
            v_stack((
                // Header
                self.create_header(),
                
                // Messages list
                scroll(
                    v_stack((
                        // Messages content
                        label(move || {
                            let filtered_messages = Self::filter_messages(
                                &messages.get(),
                                &filter_type.get(),
                                &search_query.get()
                            );
                            format!("Messaggi: {}", filtered_messages.len())
                        }),
                    ))
                    .style(move |s| s.flex_col().gap(2.0))
                )
                .style(|s| s.flex_grow(1.0)),
            ))
            .style(move |s| s.flex_col().size_full())
        )
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background_secondary)
                .border_right(1.0)
                .border_color(theme.colors.border)
        })
    }

    /// Crea l'header del pannello
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(
            label(|| "Output".to_string())
                .style(move |s| {
                    s.font_size(14.0)
                        .font_weight(Weight::BOLD)
                        .color(theme.colors.text)
                })
        )
        .style(move |s| {
            s.padding(8.0)
                .background(theme.colors.background_tertiary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_center()
        })
    }

    /// Filtra i messaggi in base ai criteri
    fn filter_messages(
        messages: &VecDeque<OutputMessage>,
        filter_type: &Option<OutputType>,
        search_query: &str,
    ) -> Vec<OutputMessage> {
        messages
            .iter()
            .filter(|msg| {
                // Filtro per tipo
                let type_match = filter_type.as_ref().map_or(true, |filter| &msg.message_type == filter);
                
                // Filtro per ricerca
                let search_match = search_query.is_empty() || 
                    msg.content.to_lowercase().contains(&search_query.to_lowercase()) ||
                    msg.source.as_ref().map_or(false, |s| s.to_lowercase().contains(&search_query.to_lowercase()));

                type_match && search_match
            })
            .cloned()
            .collect()
    }

    /// Ottiene il colore per il tipo di messaggio
    fn get_message_color(&self, message_type: &OutputType) -> floem::peniko::Color {
        match message_type {
            OutputType::Info => floem::peniko::Color::rgb8(100, 149, 237),
            OutputType::Warning => floem::peniko::Color::rgb8(255, 193, 7),
            OutputType::Error => floem::peniko::Color::rgb8(220, 53, 69),
            OutputType::Success => floem::peniko::Color::rgb8(40, 167, 69),
            OutputType::Debug => floem::peniko::Color::rgb8(108, 117, 125),
            OutputType::Build => floem::peniko::Color::rgb8(23, 162, 184),
            OutputType::Test => floem::peniko::Color::rgb8(111, 66, 193),
            OutputType::Command => floem::peniko::Color::rgb8(52, 58, 64),
        }
    }

    /// Ottiene l'icona per il tipo di messaggio
    fn get_message_icon(&self, message_type: &OutputType) -> &'static str {
        match message_type {
            OutputType::Info => "ℹ",
            OutputType::Warning => "⚠",
            OutputType::Error => "✗",
            OutputType::Success => "✓",
            OutputType::Debug => "🐛",
            OutputType::Build => "🔨",
            OutputType::Test => "🧪",
            OutputType::Command => "▶",
        }
    }

    /// Esporta i messaggi in formato testo
    pub fn export_messages(&self) -> String {
        let messages = self.messages.get();
        messages
            .iter()
            .map(|msg| {
                format!(
                    "[{}] [{}] {}{}",
                    msg.timestamp.format("%H:%M:%S"),
                    format!("{:?}", msg.message_type).to_uppercase(),
                    msg.content,
                    msg.source.as_ref().map_or(String::new(), |s| format!(" ({})", s))
                )
            })
            .collect::<Vec<_>>()
            .join("\n")
    }
}
