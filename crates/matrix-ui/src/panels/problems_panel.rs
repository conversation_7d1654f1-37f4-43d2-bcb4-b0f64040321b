//! Problems Panel per MATRIX IDE
//!
//! Pannello per visualizzare errori, warning e altri problemi del codice.

use std::sync::Arc;
use std::path::PathBuf;

use floem::{
    reactive::{RwSignal, create_rw_signal, SignalGet, SignalUpdate},
    views::{container, label, v_stack, scroll, Decorators},
    style::{AlignItems, JustifyContent},
    text::Weight,
    View,
};

use crate::{
    theme::{Theme, ThemeManager},
    error::UiError,
};

use matrix_core::Engine as CoreEngine;

/// Severità del problema
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum ProblemSeverity {
    Error,
    Warning,
    Info,
    Hint,
}

/// Categoria del problema
#[derive(Debug, Clone, PartialEq)]
pub enum ProblemCategory {
    Syntax,
    Type,
    Lint,
    Security,
    Performance,
    Style,
    Documentation,
    Test,
    Build,
}

/// Problema rilevato nel codice
#[derive(Debug, <PERSON><PERSON>)]
pub struct Problem {
    pub id: String,
    pub severity: ProblemSeverity,
    pub category: ProblemCategory,
    pub message: String,
    pub file_path: PathBuf,
    pub line: u32,
    pub column: u32,
    pub end_line: Option<u32>,
    pub end_column: Option<u32>,
    pub source: String, // LSP, linter, compiler, etc.
    pub code: Option<String>, // Error code (e.g., E0308)
    pub suggestion: Option<String>,
    pub related_information: Vec<RelatedInformation>,
}

/// Informazioni correlate al problema
#[derive(Debug, Clone)]
pub struct RelatedInformation {
    pub file_path: PathBuf,
    pub line: u32,
    pub column: u32,
    pub message: String,
}

/// Pannello dei problemi
pub struct ProblemsPanel {
    core: Arc<CoreEngine>,
    theme_manager: Arc<ThemeManager>,
    problems: RwSignal<Vec<Problem>>,
    filter_severity: RwSignal<Option<ProblemSeverity>>,
    filter_category: RwSignal<Option<ProblemCategory>>,
    filter_file: RwSignal<Option<PathBuf>>,
    search_query: RwSignal<String>,
    group_by_file: RwSignal<bool>,
}

impl ProblemsPanel {
    /// Crea un nuovo pannello dei problemi
    pub fn new(core: Arc<CoreEngine>, theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            core,
            theme_manager,
            problems: create_rw_signal(Vec::new()),
            filter_severity: create_rw_signal(None),
            filter_category: create_rw_signal(None),
            filter_file: create_rw_signal(None),
            search_query: create_rw_signal(String::new()),
            group_by_file: create_rw_signal(true),
        })
    }

    /// Aggiunge un problema
    pub fn add_problem(&self, problem: Problem) {
        self.problems.update(|problems| {
            // Rimuovi problema esistente con stesso ID se presente
            problems.retain(|p| p.id != problem.id);
            problems.push(problem);
            
            // Ordina per severità e poi per file/linea
            problems.sort_by(|a, b| {
                a.severity.cmp(&b.severity)
                    .then_with(|| a.file_path.cmp(&b.file_path))
                    .then_with(|| a.line.cmp(&b.line))
            });
        });
    }

    /// Rimuove un problema
    pub fn remove_problem(&self, problem_id: &str) {
        self.problems.update(|problems| {
            problems.retain(|p| p.id != problem_id);
        });
    }

    /// Pulisce tutti i problemi
    pub fn clear_problems(&self) {
        self.problems.set(Vec::new());
    }

    /// Pulisce i problemi per un file specifico
    pub fn clear_problems_for_file(&self, file_path: &PathBuf) {
        self.problems.update(|problems| {
            problems.retain(|p| &p.file_path != file_path);
        });
    }

    /// Imposta i problemi per un file (sostituisce tutti i problemi esistenti per quel file)
    pub fn set_problems_for_file(&self, file_path: &PathBuf, new_problems: Vec<Problem>) {
        self.problems.update(|problems| {
            // Rimuovi problemi esistenti per questo file
            problems.retain(|p| &p.file_path != file_path);
            
            // Aggiungi nuovi problemi
            problems.extend(new_problems);
            
            // Riordina
            problems.sort_by(|a, b| {
                a.severity.cmp(&b.severity)
                    .then_with(|| a.file_path.cmp(&b.file_path))
                    .then_with(|| a.line.cmp(&b.line))
            });
        });
    }

    /// Imposta il filtro per severità
    pub fn set_severity_filter(&self, severity: Option<ProblemSeverity>) {
        self.filter_severity.set(severity);
    }

    /// Imposta il filtro per categoria
    pub fn set_category_filter(&self, category: Option<ProblemCategory>) {
        self.filter_category.set(category);
    }

    /// Imposta il filtro per file
    pub fn set_file_filter(&self, file_path: Option<PathBuf>) {
        self.filter_file.set(file_path);
    }

    /// Imposta la query di ricerca
    pub fn set_search_query(&self, query: String) {
        self.search_query.set(query);
    }

    /// Toggle raggruppamento per file
    pub fn toggle_group_by_file(&self) {
        self.group_by_file.update(|group| *group = !*group);
    }

    /// Crea la vista del pannello (alias per compatibilità)
    pub fn build(&self) -> impl View {
        self.create_view()
    }

    /// Crea la vista del pannello
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let problems = self.problems;
        let filter_severity = self.filter_severity;
        let filter_category = self.filter_category;
        let filter_file = self.filter_file;
        let search_query = self.search_query;

        container(
            v_stack((
                // Header
                self.create_header(),
                
                // Problems list
                scroll(
                    v_stack((
                        // Problems content
                        label(move || {
                            let filtered_problems = Self::filter_problems(
                                &problems.get(),
                                &filter_severity.get(),
                                &filter_category.get(),
                                &filter_file.get(),
                                &search_query.get()
                            );
                            
                            let error_count = filtered_problems.iter().filter(|p| p.severity == ProblemSeverity::Error).count();
                            let warning_count = filtered_problems.iter().filter(|p| p.severity == ProblemSeverity::Warning).count();
                            
                            format!("Problemi: {} errori, {} warning, {} totali", 
                                error_count, warning_count, filtered_problems.len())
                        }),
                    ))
                    .style(move |s| s.flex_col().gap(2.0))
                )
                .style(|s| s.flex_grow(1.0)),
            ))
            .style(move |s| s.flex_col().size_full())
        )
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background_secondary)
                .border_right(1.0)
                .border_color(theme.colors.border)
        })
    }

    /// Crea l'header del pannello
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(
            label(|| "Problemi".to_string())
                .style(move |s| {
                    s.font_size(14.0)
                        .font_weight(Weight::BOLD)
                        .color(theme.colors.text)
                })
        )
        .style(move |s| {
            s.padding(8.0)
                .background(theme.colors.background_tertiary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_center()
        })
    }

    /// Filtra i problemi in base ai criteri
    fn filter_problems(
        problems: &[Problem],
        filter_severity: &Option<ProblemSeverity>,
        filter_category: &Option<ProblemCategory>,
        filter_file: &Option<PathBuf>,
        search_query: &str,
    ) -> Vec<Problem> {
        problems
            .iter()
            .filter(|problem| {
                // Filtro per severità
                let severity_match = filter_severity.as_ref().map_or(true, |filter| &problem.severity == filter);
                
                // Filtro per categoria
                let category_match = filter_category.as_ref().map_or(true, |filter| &problem.category == filter);
                
                // Filtro per file
                let file_match = filter_file.as_ref().map_or(true, |filter| &problem.file_path == filter);
                
                // Filtro per ricerca
                let search_match = search_query.is_empty() || 
                    problem.message.to_lowercase().contains(&search_query.to_lowercase()) ||
                    problem.file_path.to_string_lossy().to_lowercase().contains(&search_query.to_lowercase()) ||
                    problem.code.as_ref().map_or(false, |code| code.to_lowercase().contains(&search_query.to_lowercase()));

                severity_match && category_match && file_match && search_match
            })
            .cloned()
            .collect()
    }

    /// Ottiene il colore per la severità
    fn get_severity_color(&self, severity: &ProblemSeverity) -> floem::peniko::Color {
        match severity {
            ProblemSeverity::Error => floem::peniko::Color::rgb8(220, 53, 69),
            ProblemSeverity::Warning => floem::peniko::Color::rgb8(255, 193, 7),
            ProblemSeverity::Info => floem::peniko::Color::rgb8(23, 162, 184),
            ProblemSeverity::Hint => floem::peniko::Color::rgb8(108, 117, 125),
        }
    }

    /// Ottiene l'icona per la severità
    fn get_severity_icon(&self, severity: &ProblemSeverity) -> &'static str {
        match severity {
            ProblemSeverity::Error => "✗",
            ProblemSeverity::Warning => "⚠",
            ProblemSeverity::Info => "ℹ",
            ProblemSeverity::Hint => "💡",
        }
    }

    /// Ottiene le statistiche dei problemi
    pub fn get_statistics(&self) -> ProblemStatistics {
        let problems = self.problems.get();
        
        let error_count = problems.iter().filter(|p| p.severity == ProblemSeverity::Error).count();
        let warning_count = problems.iter().filter(|p| p.severity == ProblemSeverity::Warning).count();
        let info_count = problems.iter().filter(|p| p.severity == ProblemSeverity::Info).count();
        let hint_count = problems.iter().filter(|p| p.severity == ProblemSeverity::Hint).count();
        
        let files_with_problems: std::collections::HashSet<_> = problems.iter().map(|p| &p.file_path).collect();
        
        ProblemStatistics {
            total_problems: problems.len(),
            error_count,
            warning_count,
            info_count,
            hint_count,
            files_with_problems: files_with_problems.len(),
        }
    }
}

/// Statistiche dei problemi
#[derive(Debug, Clone)]
pub struct ProblemStatistics {
    pub total_problems: usize,
    pub error_count: usize,
    pub warning_count: usize,
    pub info_count: usize,
    pub hint_count: usize,
    pub files_with_problems: usize,
}
