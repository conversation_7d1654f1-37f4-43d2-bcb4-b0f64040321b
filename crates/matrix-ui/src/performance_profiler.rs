//! Profiler di performance per componenti migliorati
//!
//! Strumento avanzato per misurare e ottimizzare le performance 
//! dei componenti UI migliorati di MATRIX IDE.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use std::cell::RefCell;
use std::fmt;
use std::fs::File;
use std::io::Write;
use std::path::Path;

/// Tipo di operazione misurata
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ProfilerOperation {
    /// Rendering componente
    Render(String),

    /// Costruzione componente
    Build(String),

    /// Aggiornamento componente
    Update(String),

    /// Gestione evento
    EventHandling(String),

    /// Operazione I/O
    IO(String),

    /// Operazione generica
    Generic(String),
}

impl fmt::Display for ProfilerOperation {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Self::Render(name) => write!(f, "Render({})", name),
            Self::Build(name) => write!(f, "Build({})", name),
            Self::Update(name) => write!(f, "Update({})", name),
            Self::EventHandling(name) => write!(f, "EventHandling({})", name),
            Self::IO(name) => write!(f, "IO({})", name),
            Self::Generic(name) => write!(f, "Generic({})", name),
        }
    }
}

/// Singola misurazione di performance
#[derive(Debug, Clone)]
pub struct PerformanceMeasurement {
    /// Operazione misurata
    pub operation: ProfilerOperation,

    /// Durata dell'operazione
    pub duration: Duration,

    /// Timestamp dell'inizio dell'operazione
    pub timestamp: Instant,

    /// Memoria utilizzata (in byte) se disponibile
    pub memory_usage: Option<usize>,
}

/// Statistiche per un'operazione
#[derive(Debug, Clone)]
pub struct OperationStats {
    /// Numero di volte che l'operazione è stata misurata
    pub count: usize,

    /// Durata totale dell'operazione
    pub total_duration: Duration,

    /// Durata minima dell'operazione
    pub min_duration: Duration,

    /// Durata massima dell'operazione
    pub max_duration: Duration,

    /// Durata media dell'operazione
    pub avg_duration: Duration,
}

impl OperationStats {
    /// Crea nuove statistiche
    fn new() -> Self {
        Self {
            count: 0,
            total_duration: Duration::from_nanos(0),
            min_duration: Duration::from_secs(u64::MAX),
            max_duration: Duration::from_nanos(0),
            avg_duration: Duration::from_nanos(0),
        }
    }

    /// Aggiorna le statistiche con una nuova misurazione
    fn update(&mut self, duration: Duration) {
        self.count += 1;
        self.total_duration += duration;

        if duration < self.min_duration {
            self.min_duration = duration;
        }

        if duration > self.max_duration {
            self.max_duration = duration;
        }

        // Ricalcola la media
        self.avg_duration = self.total_duration / self.count as u32;
    }
}

/// Contesto di una misurazione in corso
pub struct MeasurementContext {
    /// Operazione misurata
    operation: ProfilerOperation,

    /// Timestamp di inizio
    start_time: Instant,

    /// Profiler di riferimento
    profiler: Arc<PerformanceProfiler>,
}

impl MeasurementContext {
    /// Crea un nuovo contesto di misurazione
    fn new(operation: ProfilerOperation, profiler: Arc<PerformanceProfiler>) -> Self {
        Self {
            operation,
            start_time: Instant::now(),
            profiler,
        }
    }
}

impl Drop for MeasurementContext {
    fn drop(&mut self) {
        // Quando il contesto viene distrutto, registra la misurazione
        let duration = self.start_time.elapsed();
        self.profiler.record_measurement(
            self.operation.clone(),
            duration,
            self.start_time,
            None
        );
    }
}

/// Risultato dell'analisi del profiler
#[derive(Debug, Clone)]
pub struct ProfilerReport {
    /// Statistiche per operazione
    pub stats_by_operation: HashMap<ProfilerOperation, OperationStats>,

    /// Operazioni più lente
    pub slowest_operations: Vec<(ProfilerOperation, Duration)>,

    /// Operazioni più frequenti
    pub most_frequent_operations: Vec<(ProfilerOperation, usize)>,

    /// Durata totale delle misurazioni
    pub total_duration: Duration,

    /// Numero totale di operazioni
    pub total_operations: usize,

    /// Timestamp generazione report
    pub timestamp: Instant,
}

/// Profiler di performance
pub struct PerformanceProfiler {
    /// Misurazioni registrate
    measurements: Mutex<Vec<PerformanceMeasurement>>,

    /// Flag attivazione
    enabled: Mutex<bool>,

    /// Thread-local storage per il contesto attuale
    current_context: thread_local::ThreadLocal<RefCell<Option<MeasurementContext>>>,
}

impl PerformanceProfiler {
    /// Crea un nuovo profiler
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            measurements: Mutex::new(Vec::new()),
            enabled: Mutex::new(true),
            current_context: thread_local::ThreadLocal::new(),
        })
    }

    /// Attiva il profiler
    pub fn enable(&self) {
        let mut enabled = self.enabled.lock().unwrap();
        *enabled = true;
    }

    /// Disattiva il profiler
    pub fn disable(&self) {
        let mut enabled = self.enabled.lock().unwrap();
        *enabled = false;
    }

    /// Verifica se il profiler è attivo
    pub fn is_enabled(&self) -> bool {
        *self.enabled.lock().unwrap()
    }

    /// Avvia la misurazione di un'operazione
    pub fn start_measurement(&self, operation: ProfilerOperation) -> Option<Arc<Self>> {
        // Se il profiler è disattivato, non fare nulla
        if !self.is_enabled() {
            return None;
        }

        // Crea un nuovo contesto di misurazione
        let context = MeasurementContext::new(operation, Arc::new(self.clone()));

        // Imposta il contesto corrente
        if let Some(cell) = self.current_context.get() {
            *cell.borrow_mut() = Some(context);
        } else {
            self.current_context.get_or(|| RefCell::new(Some(context)));
        }

        Some(Arc::new(self.clone()))
    }

    /// Registra una misurazione
    pub fn record_measurement(
        &self,
        operation: ProfilerOperation,
        duration: Duration,
        timestamp: Instant,
        memory_usage: Option<usize>
    ) {
        // Se il profiler è disattivato, non fare nulla
        if !self.is_enabled() {
            return;
        }

        let measurement = PerformanceMeasurement {
            operation,
            duration,
            timestamp,
            memory_usage,
        };

        let mut measurements = self.measurements.lock().unwrap();
        measurements.push(measurement);
    }

    /// Misura il tempo di esecuzione di una funzione
    pub fn measure<F, T>(&self, operation: ProfilerOperation, f: F) -> T
    where
        F: FnOnce() -> T
    {
        // Se il profiler è disattivato, esegui direttamente la funzione
        if !self.is_enabled() {
            return f();
        }

        let start = Instant::now();
        let result = f();
        let duration = start.elapsed();

        self.record_measurement(operation, duration, start, None);

        result
    }

    /// Pulisce le misurazioni
    pub fn clear_measurements(&self) {
        let mut measurements = self.measurements.lock().unwrap();
        measurements.clear();
    }

    /// Genera un report delle performance
    pub fn generate_report(&self) -> ProfilerReport {
        let measurements = self.measurements.lock().unwrap();

        // Calcola statistiche per operazione
        let mut stats_by_operation: HashMap<ProfilerOperation, OperationStats> = HashMap::new();
        let mut total_duration = Duration::from_nanos(0);

        for measurement in measurements.iter() {
            let stats = stats_by_operation
                .entry(measurement.operation.clone())
                .or_insert_with(OperationStats::new);

            stats.update(measurement.duration);
            total_duration += measurement.duration;
        }

        // Trova operazioni più lente
        let mut slowest_operations: Vec<(ProfilerOperation, Duration)> = stats_by_operation
            .iter()
            .map(|(op, stats)| (op.clone(), stats.max_duration))
            .collect();

        slowest_operations.sort_by(|a, b| b.1.cmp(&a.1));
        let slowest_operations = slowest_operations.into_iter().take(10).collect();

        // Trova operazioni più frequenti
        let mut most_frequent_operations: Vec<(ProfilerOperation, usize)> = stats_by_operation
            .iter()
            .map(|(op, stats)| (op.clone(), stats.count))
            .collect();

        most_frequent_operations.sort_by(|a, b| b.1.cmp(&a.1));
        let most_frequent_operations = most_frequent_operations.into_iter().take(10).collect();

        ProfilerReport {
            stats_by_operation,
            slowest_operations,
            most_frequent_operations,
            total_duration,
            total_operations: measurements.len(),
            timestamp: Instant::now(),
        }
    }

    /// Esporta il report in formato Markdown
    pub fn export_report_markdown<P: AsRef<Path>>(&self, path: P) -> std::io::Result<()> {
        let report = self.generate_report();
        let mut file = File::create(path)?;

        writeln!(file, "# MATRIX IDE Performance Report")?;
        writeln!(file, "")?;
        writeln!(file, "## Sommario")?;
        writeln!(file, "")?;
        writeln!(file, "- **Operazioni totali**: {}", report.total_operations)?;
        writeln!(file, "- **Durata totale**: {:?}", report.total_duration)?;
        writeln!(file, "- **Timestamp**: {:?}", report.timestamp)?;
        writeln!(file, "")?;

        writeln!(file, "## Operazioni più lente")?;
        writeln!(file, "")?;
        writeln!(file, "| Operazione | Durata max |")?;
        writeln!(file, "|------------|------------|")?;

        for (op, duration) in &report.slowest_operations {
            writeln!(file, "| {} | {:?} |", op, duration)?;
        }

        writeln!(file, "")?;
        writeln!(file, "## Operazioni più frequenti")?;
        writeln!(file, "")?;
        writeln!(file, "| Operazione | Conteggio |")?;
        writeln!(file, "|------------|-----------|")?;

        for (op, count) in &report.most_frequent_operations {
            writeln!(file, "| {} | {} |", op, count)?;
        }

        writeln!(file, "")?;
        writeln!(file, "## Statistiche dettagliate")?;
        writeln!(file, "")?;
        writeln!(file, "| Operazione | Conteggio | Media | Min | Max | Totale |")?;
        writeln!(file, "|------------|-----------|-------|-----|-----|--------|")?;

        for (op, stats) in &report.stats_by_operation {
            writeln!(file, "| {} | {} | {:?} | {:?} | {:?} | {:?} |",
                op, stats.count, stats.avg_duration, stats.min_duration, 
                stats.max_duration, stats.total_duration)?;
        }

        Ok(())
    }
}

impl Clone for PerformanceProfiler {
    fn clone(&self) -> Self {
        Self {
            measurements: Mutex::new(self.measurements.lock().unwrap().clone()),
            enabled: Mutex::new(*self.enabled.lock().unwrap()),
            current_context: thread_local::ThreadLocal::new(),
        }
    }
}

/// Macro per misurare facilmente un blocco di codice
#[macro_export]
macro_rules! profile {
    ($profiler:expr, $op_type:ident, $name:expr, $code:block) => {
        if let Some(profiler) = $profiler.as_ref() {
            if profiler.is_enabled() {
                profiler.measure(
                    ProfilerOperation::$op_type($name.to_string()),
                    || $code
                )
            } else {
                $code
            }
        } else {
            $code
        }
    };
}

/// Componente profilabile
pub trait Profilable {
    /// Ottiene il profiler associato
    fn profiler(&self) -> Option<Arc<PerformanceProfiler>>;

    /// Imposta il profiler
    fn set_profiler(&mut self, profiler: Arc<PerformanceProfiler>);

    /// Abilita il profiling
    fn enable_profiling(&self) {
        if let Some(profiler) = self.profiler() {
            profiler.enable();
        }
    }

    /// Disabilita il profiling
    fn disable_profiling(&self) {
        if let Some(profiler) = self.profiler() {
            profiler.disable();
        }
    }

    /// Pulisce i dati di profiling
    fn clear_profiling_data(&self) {
        if let Some(profiler) = self.profiler() {
            profiler.clear_measurements();
        }
    }

    /// Genera un report di profiling
    fn generate_profiling_report(&self) -> Option<ProfilerReport> {
        self.profiler().map(|p| p.generate_report())
    }
}
