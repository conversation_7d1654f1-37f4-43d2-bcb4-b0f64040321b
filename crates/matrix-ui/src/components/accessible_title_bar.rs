//! Title Bar migliorato con supporto accessibilità
//!
//! Implementazione ottimizzata della barra del titolo con menu dropdown nativi
//! e supporto completo per screen reader e altre tecnologie assistive.

use std::sync::Arc;
use std::path::PathBuf;

use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{
        container, h_stack, h_stack_from_iter, label, empty, stack, v_stack, v_stack_from_iter,
        dropdown, Decorators, tooltip
    },
    style::{AlignItems, JustifyContent, CursorStyle, Position, Display},
    text::Weight,
    View,
    prelude::Color,
    event::{EventListener, Event, EventPropagation},
};

use crate::{
    theme::{ThemeManager, Theme},
    error::UiError,
    accessibility::{
        AccessibilityManager, AccessibilityProps, AriaRole, AriaState
    },
    performance_profiler::{PerformanceProfiler, ProfilerOperation},
};

/// Elemento menu
#[derive(Clone)]
pub struct MenuItem {
    /// Etichetta elemento
    pub label: String,

    /// Elemento attivo
    pub enabled: bool,

    /// Azione associata
    pub action: Option<String>,

    /// Separatore dopo questo elemento
    pub separator_after: bool,

    /// Scorciatoia da tastiera (opzionale)
    pub shortcut: Option<String>,

    /// ID accessibilità
    pub accessibility_id: Option<String>,
}

impl MenuItem {
    /// Crea un nuovo elemento menu
    pub fn new(label: &str) -> Self {
        Self {
            label: label.to_string(),
            enabled: true,
            action: None,
            separator_after: false,
            shortcut: None,
            accessibility_id: None,
        }
    }

    /// Imposta l'azione
    pub fn with_action(mut self, action: &str) -> Self {
        self.action = Some(action.to_string());
        self
    }

    /// Imposta se l'elemento è attivo
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// Aggiunge un separatore dopo questo elemento
    pub fn with_separator(mut self) -> Self {
        self.separator_after = true;
        self
    }

    /// Aggiunge una scorciatoia da tastiera
    pub fn with_shortcut(mut self, shortcut: &str) -> Self {
        self.shortcut = Some(shortcut.to_string());
        self
    }
}

/// Menu dropdown
#[derive(Clone)]
pub struct DropdownMenu {
    /// Titolo menu
    pub title: String,

    /// Elementi menu
    pub items: Vec<MenuItem>,

    /// ID accessibilità
    pub accessibility_id: Option<String>,
}

impl DropdownMenu {
    /// Crea un nuovo menu
    pub fn new(title: &str, items: Vec<MenuItem>) -> Self {
        Self {
            title: title.to_string(),
            items,
            accessibility_id: None,
        }
    }
}

/// Configurazione Title Bar
pub struct TitleBarConfig {
    /// Altezza della title bar
    pub height: f64,

    /// Mostra il logo MATRIX
    pub show_logo: bool,

    /// Mostra i controlli finestra
    pub show_window_controls: bool,

    /// Titolo personalizzato
    pub custom_title: Option<String>,

    /// Livello di accessibilità
    pub accessibility_level: crate::accessibility::AccessibilityLevel,
}

impl Default for TitleBarConfig {
    fn default() -> Self {
        Self {
            height: 37.0,
            show_logo: true,
            show_window_controls: !cfg!(target_os = "macos"),
            custom_title: None,
            accessibility_level: crate::accessibility::AccessibilityLevel::Basic,
        }
    }
}

/// Componente Title Bar migliorato con supporto accessibilità
pub struct AccessibleTitleBar {
    /// Configurazione
    config: TitleBarConfig,

    /// Gestore temi
    theme_manager: Arc<ThemeManager>,

    /// Gestore accessibilità
    accessibility_manager: Arc<AccessibilityManager>,

    /// Profiler performance (opzionale)
    performance_profiler: Option<Arc<PerformanceProfiler>>,

    /// Stato della finestra (massimizzata o no)
    window_maximized: RwSignal<bool>,

    /// Titolo corrente
    current_title: RwSignal<String>,

    /// Menu attualmente selezionato
    active_menu: RwSignal<Option<String>>,

    /// Menu disponibili
    menus: RwSignal<Vec<DropdownMenu>>,
}

impl AccessibleTitleBar {
    /// Crea una nuova title bar accessibile
    pub fn new(
        theme_manager: Arc<ThemeManager>,
        accessibility_manager: Arc<AccessibilityManager>,
    ) -> Result<Self, UiError> {
        Ok(Self {
            config: TitleBarConfig::default(),
            theme_manager,
            accessibility_manager,
            performance_profiler: None,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
            active_menu: create_rw_signal(None),
            menus: create_rw_signal(Vec::new()),
        })
    }

    /// Crea una title bar con configurazione personalizzata
    pub fn with_config(
        theme_manager: Arc<ThemeManager>,
        accessibility_manager: Arc<AccessibilityManager>,
        config: TitleBarConfig,
    ) -> Result<Self, UiError> {
        Ok(Self {
            config,
            theme_manager,
            accessibility_manager,
            performance_profiler: None,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
            active_menu: create_rw_signal(None),
            menus: create_rw_signal(Vec::new()),
        })
    }

    /// Aggiunge il profiler di performance
    pub fn with_performance_profiler(mut self, profiler: Arc<PerformanceProfiler>) -> Self {
        self.performance_profiler = Some(profiler);
        self
    }

    /// Imposta il titolo corrente
    pub fn set_title(&self, title: &str) {
        self.current_title.set(title.to_string());
    }

    /// Imposta i menu
    pub fn set_menus(&self, menus: Vec<DropdownMenu>) {
        // Assegna ID di accessibilità ai menu se necessario
        let menus_with_accessibility = menus.into_iter().map(|menu| {
            let mut menu = menu.clone();

            // Crea ID di accessibilità per il menu se non esiste
            if menu.accessibility_id.is_none() {
                menu.accessibility_id = Some(self.accessibility_manager.generate_id("menu"));
            }

            // Crea ID di accessibilità per gli elementi del menu se non esistono
            let items_with_accessibility = menu.items.into_iter().map(|item| {
                let mut item = item.clone();
                if item.accessibility_id.is_none() {
                    item.accessibility_id = Some(self.accessibility_manager.generate_id("menu-item"));
                }
                item
            }).collect();

            // Aggiorna gli elementi del menu
            menu.items = items_with_accessibility;
            menu
        }).collect();

        self.menus.set(menus_with_accessibility);
    }

    /// Costruisci menu standard
    pub fn build_standard_menus(&self) -> Vec<DropdownMenu> {
        vec![
            DropdownMenu::new("File", vec![
                MenuItem::new("Nuovo").with_action("file.new").with_shortcut("Ctrl+N"),
                MenuItem::new("Apri...").with_action("file.open").with_shortcut("Ctrl+O"),
                MenuItem::new("Salva").with_action("file.save").with_shortcut("Ctrl+S"),
                MenuItem::new("Salva con nome...").with_action("file.save_as").with_shortcut("Ctrl+Shift+S").with_separator(),
                MenuItem::new("Preferenze").with_action("file.preferences").with_separator(),
                MenuItem::new("Esci").with_action("file.exit").with_shortcut("Alt+F4"),
            ]),
            DropdownMenu::new("Modifica", vec![
                MenuItem::new("Annulla").with_action("edit.undo").with_shortcut("Ctrl+Z"),
                MenuItem::new("Ripeti").with_action("edit.redo").with_shortcut("Ctrl+Y").with_separator(),
                MenuItem::new("Taglia").with_action("edit.cut").with_shortcut("Ctrl+X"),
                MenuItem::new("Copia").with_action("edit.copy").with_shortcut("Ctrl+C"),
                MenuItem::new("Incolla").with_action("edit.paste").with_shortcut("Ctrl+V").with_separator(),
                MenuItem::new("Trova...").with_action("edit.find").with_shortcut("Ctrl+F"),
                MenuItem::new("Sostituisci...").with_action("edit.replace").with_shortcut("Ctrl+H"),
            ]),
            DropdownMenu::new("Visualizza", vec![
                MenuItem::new("Pannello file").with_action("view.file_panel"),
                MenuItem::new("Pannello terminale").with_action("view.terminal_panel"),
                MenuItem::new("Pannello output").with_action("view.output_panel").with_separator(),
                MenuItem::new("Zoom in").with_action("view.zoom_in").with_shortcut("Ctrl++"),
                MenuItem::new("Zoom out").with_action("view.zoom_out").with_shortcut("Ctrl+-"),
                MenuItem::new("Reimposta zoom").with_action("view.zoom_reset").with_shortcut("Ctrl+0"),
            ]),
            DropdownMenu::new("Accessibilità", vec![
                MenuItem::new("Modalità focus").with_action("accessibility.focus_mode"),
                MenuItem::new("Aumenta contrasto").with_action("accessibility.high_contrast"),
                MenuItem::new("Aumenta dimensione testo").with_action("accessibility.larger_text").with_shortcut("Ctrl+Shift++"),
                MenuItem::new("Riduci dimensione testo").with_action("accessibility.smaller_text").with_shortcut("Ctrl+Shift+-"),
                MenuItem::new("Lettura schermo").with_action("accessibility.screen_reader").with_separator(),
                MenuItem::new("Impostazioni accessibilità").with_action("accessibility.settings"),
            ]),
            DropdownMenu::new("Aiuto", vec![
                MenuItem::new("Documentazione").with_action("help.docs"),
                MenuItem::new("Scorciatoie").with_action("help.shortcuts").with_separator(),
                MenuItem::new("Informazioni").with_action("help.about"),
            ]),
        ]
    }

    /// Crea menu dropdown nativo accessibile
    fn create_accessible_dropdown_menu(
        &self,
        menu: DropdownMenu,
        theme: Arc<Theme>,
        active_menu: RwSignal<Option<String>>,
    ) -> impl View {
        // Crea proprietà accessibilità per il menu
        let menu_id = menu.accessibility_id.clone().unwrap_or_else(|| 
            self.accessibility_manager.generate_id("menu"));

        let accessibility_props = self.accessibility_manager.create_menu_item_props(
            &menu.title, 
            None
        );

        // Registra il menu per l'accessibilità
        self.accessibility_manager.register_component(accessibility_props.clone());

        // Clona per closure
        let menu_title = menu.title.clone();
        let menu_items = menu.items.clone();

        // Crea il menu dropdown con supporto accessibilità - versione semplificata per Floem 0.2
        let selected_item = create_rw_signal(menu_title.clone());
        let menu_labels: Vec<String> = menu_items.iter().map(|item| item.label.clone()).collect();

        // Versione semplificata per Floem 0.2 - solo un label per ora
        label(move || menu_title.clone())
            .style(move |s| {
                s.padding_horiz(12.0)
                 .padding_vert(8.0)
                 .cursor(CursorStyle::Pointer)
                 .color(theme.colors.text)
            })
    }

    /// Crea la vista della title bar accessibile
    pub fn build(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let theme = Arc::new(theme);
        let window_maximized = self.window_maximized;
        let show_logo = self.config.show_logo;
        let show_window_controls = self.config.show_window_controls;
        let current_title = self.current_title;
        let custom_title = self.config.custom_title.clone();
        let height = self.config.height;
        let menus = self.menus;
        let active_menu = create_rw_signal(None::<String>);

        // Se configurato, profila la costruzione della view
        let build_view = || {
            let theme1 = theme.clone();
            let theme2 = theme.clone();
            let theme3 = theme.clone();
            let theme4 = theme.clone();
            let theme5 = theme.clone();
            let theme6 = theme.clone();
            let theme7 = theme.clone();
            let theme8 = theme.clone();
            
            // Crea ID accessibilità per la title bar
            let _title_bar_id = self.accessibility_manager.generate_id("title-bar");

            // Crea la vista
            container(
                h_stack((
                    // Logo e titolo (lato sinistro)
                    container(
                        h_stack((
                            container(
                                if show_logo {
                                    h_stack((
                                        // Logo MATRIX
                                        label(|| "MATRIX".to_string())
                                            .style(move |s| {
                                                let theme1_clone = theme1.clone();
                                                s.font_size(16.0)
                                                 .font_weight(Weight::BOLD)
                                                 .color(theme1_clone.colors.accent)
                                                 .margin_right(6.0)
                                            }),
                                        // IDE
                                        label(|| "IDE".to_string())
                                            .style(move |s| {
                                                let theme2_clone = theme2.clone();
                                                s.font_size(16.0)
                                                 .font_weight(Weight::NORMAL)
                                                 .color(theme2_clone.colors.text)
                                            }),
                                    ))
                                    .style(|s| s.margin_right(20.0))
                                } else {
                                    empty()
                                }
                            ),

                            // Menu dropdown
                            h_stack_from_iter(
                                menus.get().into_iter().map({
                                    let theme3 = theme3.clone();
                                    let active_menu = active_menu.clone();
                                    move |menu| {
                                        self.create_accessible_dropdown_menu(
                                            menu, theme3.clone(), active_menu.clone()
                                        )
                                    }
                                })
                            )
                            .style(|s| s.gap(2.0))
                        ))
                        .style(|s| s.align_items(Some(AlignItems::Center)))
                    )
                    .style(|s| s.flex_grow(1.0)),

                    // Titolo centrale
                    container(
                        label(move || {
                            if let Some(title) = &custom_title {
                                title.clone()
                            } else {
                                current_title.get()
                            }
                        })
                        .style(move |s| {
                            let theme4_clone = theme4.clone();
                            s.font_size(14.0)
                             .color(theme4_clone.colors.text_secondary)
                        })
                    )
                    .style(|s| s.align_items(Some(AlignItems::Center))),

                    // Controlli finestra (lato destro)
                    if show_window_controls {
                        container(
                            h_stack((
                                // Pulsante minimizza
                                container(
                                    label(|| "—".to_string())
                                )
                                .style(move |s| {
                                    let theme5_clone = theme5.clone();
                                    s.padding(8.0)
                                     .cursor(CursorStyle::Pointer)
                                     .hover(move |s| {
                                         let theme5_clone2 = theme5_clone.clone();
                                         s.background(theme5_clone2.colors.hover_bg)
                                     })
                                     .color(theme5_clone.colors.text)
                                }),

                                // Pulsante massimizza/ripristina
                                container(
                                    label({
                                        let window_maximized = window_maximized.clone();
                                        move || {
                                            if window_maximized.get() {
                                                "❐".to_string()
                                            } else {
                                                "□".to_string()
                                            }
                                        }
                                    })
                                )
                                .style({
                                    let theme6 = theme6.clone();
                                    move |s| {
                                        let theme6_clone = theme6.clone();
                                        s.padding(8.0)
                                         .cursor(CursorStyle::Pointer)
                                         .hover(move |s| {
                                             let theme6_clone2 = theme6_clone.clone();
                                             s.background(theme6_clone2.colors.hover_bg)
                                         })
                                         .color(theme6.colors.text)
                                    }
                                }),

                                // Pulsante chiudi
                                container(
                                    label(|| "×".to_string())
                                )
                                .style(move |s| {
                                    let theme7_clone = theme7.clone();
                                    s.padding(8.0)
                                     .cursor(CursorStyle::Pointer)
                                     .hover(move |s| {
                                         let theme7_clone2 = theme7_clone.clone();
                                         s.background(theme7_clone2.colors.error)
                                     })
                                     .color(theme7_clone.colors.text)
                                }),
                            ))
                            .style(|s| s.align_items(Some(AlignItems::Center)))
                        )
                        .style(|s| s.margin_left(20.0))
                    } else {
                        container(empty())
                    }
                ))
                .style(move |s| {
                    let theme8_clone = theme8.clone();
                    s.width_full()
                     .height(height)
                     .padding_horiz(8.0)
                     .background(theme8_clone.colors.title_bar_bg)
                     .border_bottom(1.0)
                     .border_color(theme8_clone.colors.border)
                })
            )
        };

        // Se disponibile, usa il profiler
        if let Some(profiler) = &self.performance_profiler {
            Ok(profiler.measure(
                ProfilerOperation::Build("AccessibleTitleBar".to_string()),
                build_view
            ))
        } else {
            Ok(build_view())
        }
    }
}
