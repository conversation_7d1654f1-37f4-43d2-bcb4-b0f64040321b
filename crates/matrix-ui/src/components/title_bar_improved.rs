//! MATRIX_IDE Title Bar Component
//!
//! Implementazione professionale della barra del titolo per MATRIX_IDE.
//! Ottimizzata per Floem 0.2 API native con menu dropdown funzionanti.

use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{
        container, h_stack, h_stack_from_iter, label, empty, stack, v_stack, v_stack_from_iter,
        dropdown, Decorators
    },
    style::{AlignItems, JustifyContent, CursorStyle, Position, Display},
    text::Weight,
    View,
    prelude::Color,
    event::{EventListener, Event, EventPropagation},
};

use crate::{
    theme::{ThemeManager, Theme},
    error::UiError,
    editor::AdvancedEditor,
};
use matrix_core::Engine as CoreEngine;

// ============================================================================
// GLOBAL APPLICATION STATE
// ============================================================================

/// Stato globale dell'applicazione per l'accesso dai menu actions
static GLOBAL_APP_STATE: Mutex<Option<Arc<GlobalAppState>>> = Mutex::new(None);

/// Stato globale dell'applicazione
pub struct GlobalAppState {
    /// Core engine
    pub core: Arc<CoreEngine>,
    /// Theme manager
    pub theme_manager: Arc<ThemeManager>,
    /// Editor avanzato
    pub editor: Arc<AdvancedEditor>,
}

impl GlobalAppState {
    /// Crea una nuova istanza dello stato globale
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
        editor: Arc<AdvancedEditor>,
    ) -> Self {
        Self {
            core,
            theme_manager,
            editor,
        }
    }

    /// Inizializza lo stato globale
    pub fn initialize(state: Arc<Self>) {
        let mut global = GLOBAL_APP_STATE.lock().unwrap();
        *global = Some(state);
    }

    /// Ottiene lo stato globale
    pub fn get() -> Option<Arc<Self>> {
        let global = GLOBAL_APP_STATE.lock().unwrap();
        global.clone()
    }
}

// ============================================================================
// MENU STRUCTURES
// ============================================================================

/// Elemento menu
#[derive(Clone)]
pub struct MenuItem {
    /// Etichetta elemento
    pub label: String,
    /// Elemento attivo
    pub enabled: bool,
    /// Azione associata
    pub action: Option<String>,
    /// Separatore dopo questo elemento
    pub separator_after: bool,
    /// Scorciatoia da tastiera (opzionale)
    pub shortcut: Option<String>,
}

impl MenuItem {
    /// Crea un nuovo elemento menu
    pub fn new(label: &str) -> Self {
        Self {
            label: label.to_string(),
            enabled: true,
            action: None,
            separator_after: false,
            shortcut: None,
        }
    }

    /// Imposta l'azione
    pub fn with_action(mut self, action: &str) -> Self {
        self.action = Some(action.to_string());
        self
    }

    /// Imposta se l'elemento è attivo
    pub fn enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }

    /// Aggiunge un separatore dopo questo elemento
    pub fn with_separator(mut self) -> Self {
        self.separator_after = true;
        self
    }

    /// Aggiunge una scorciatoia da tastiera
    pub fn with_shortcut(mut self, shortcut: &str) -> Self {
        self.shortcut = Some(shortcut.to_string());
        self
    }
}

/// Menu dropdown
#[derive(Clone)]
pub struct DropdownMenu {
    /// Titolo menu
    pub title: String,
    /// Elementi menu
    pub items: Vec<MenuItem>,
}

impl DropdownMenu {
    /// Crea un nuovo menu
    pub fn new(title: &str, items: Vec<MenuItem>) -> Self {
        Self {
            title: title.to_string(),
            items,
        }
    }
}

// ============================================================================
// TITLE BAR CONFIGURATION
// ============================================================================

/// Configurazione Title Bar
pub struct TitleBarConfig {
    /// Altezza della title bar
    pub height: f64,
    /// Mostra il logo MATRIX
    pub show_logo: bool,
    /// Mostra i controlli finestra
    pub show_window_controls: bool,
    /// Titolo personalizzato
    pub custom_title: Option<String>,
}

impl Default for TitleBarConfig {
    fn default() -> Self {
        Self {
            height: 37.0, // Stessa altezza di Lapce
            show_logo: true,
            show_window_controls: !cfg!(target_os = "macos"), // Su macOS usa i controlli nativi
            custom_title: None,
        }
    }
}

/// Componente Title Bar per MATRIX_IDE
pub struct TitleBar {
    /// Configurazione
    config: TitleBarConfig,
    /// Gestore temi
    theme_manager: Arc<ThemeManager>,
    /// Stato della finestra (massimizzata o no)
    window_maximized: RwSignal<bool>,
    /// Titolo corrente
    current_title: RwSignal<String>,
    /// Menu attualmente selezionato
    active_menu: RwSignal<Option<String>>,
}

impl TitleBar {
    /// Crea una nuova title bar
    pub fn new(theme_manager: Arc<ThemeManager>) -> Result<Self, UiError> {
        Ok(Self {
            config: TitleBarConfig::default(),
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
            active_menu: create_rw_signal(None),
        })
    }

    /// Crea una title bar con configurazione personalizzata
    pub fn with_config(
        theme_manager: Arc<ThemeManager>,
        config: TitleBarConfig,
    ) -> Result<Self, UiError> {
        Ok(Self {
            config,
            theme_manager,
            window_maximized: create_rw_signal(false),
            current_title: create_rw_signal("MATRIX_IDE".to_string()),
            active_menu: create_rw_signal(None),
        })
    }

    /// Imposta il titolo corrente
    pub fn set_title(&self, title: &str) {
        self.current_title.set(title.to_string());
    }

    /// Crea la vista della title bar
    pub fn build(self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        let window_maximized = self.window_maximized;
        let active_menu = self.active_menu;
        let show_logo = self.config.show_logo;
        let show_window_controls = self.config.show_window_controls;
        let current_title = self.current_title;
        let custom_title = self.config.custom_title.clone();
        let config_height = self.config.height;

        // Costruisci i menu standard
        let menus = vec![
            DropdownMenu::new("File", vec![
                MenuItem::new("Nuovo").with_action("file.new").with_shortcut("Ctrl+N"),
                MenuItem::new("Apri...").with_action("file.open").with_shortcut("Ctrl+O"),
                MenuItem::new("Salva").with_action("file.save").with_shortcut("Ctrl+S"),
                MenuItem::new("Salva con nome...").with_action("file.save_as").with_shortcut("Ctrl+Shift+S").with_separator(),
                MenuItem::new("Preferenze").with_action("file.preferences").with_separator(),
                MenuItem::new("Esci").with_action("file.exit").with_shortcut("Alt+F4"),
            ]),
            DropdownMenu::new("Modifica", vec![
                MenuItem::new("Annulla").with_action("edit.undo").with_shortcut("Ctrl+Z"),
                MenuItem::new("Ripeti").with_action("edit.redo").with_shortcut("Ctrl+Y").with_separator(),
                MenuItem::new("Taglia").with_action("edit.cut").with_shortcut("Ctrl+X"),
                MenuItem::new("Copia").with_action("edit.copy").with_shortcut("Ctrl+C"),
                MenuItem::new("Incolla").with_action("edit.paste").with_shortcut("Ctrl+V").with_separator(),
                MenuItem::new("Trova...").with_action("edit.find").with_shortcut("Ctrl+F"),
                MenuItem::new("Sostituisci...").with_action("edit.replace").with_shortcut("Ctrl+H"),
            ]),
            DropdownMenu::new("Visualizza", vec![
                MenuItem::new("Pannello file").with_action("view.file_panel"),
                MenuItem::new("Pannello terminale").with_action("view.terminal_panel"),
                MenuItem::new("Pannello output").with_action("view.output_panel").with_separator(),
                MenuItem::new("Zoom in").with_action("view.zoom_in").with_shortcut("Ctrl++"),
                MenuItem::new("Zoom out").with_action("view.zoom_out").with_shortcut("Ctrl+-"),
                MenuItem::new("Reimposta zoom").with_action("view.zoom_reset").with_shortcut("Ctrl+0"),
            ]),
            DropdownMenu::new("Progetto", vec![
                MenuItem::new("Apri progetto...").with_action("project.open"),
                MenuItem::new("Chiudi progetto").with_action("project.close").with_separator(),
                MenuItem::new("Esegui").with_action("project.run").with_shortcut("F5"),
                MenuItem::new("Compila").with_action("project.build").with_shortcut("Ctrl+B"),
                MenuItem::new("Pulisci").with_action("project.clean"),
            ]),
            DropdownMenu::new("Assistente", vec![
                MenuItem::new("Completa codice").with_action("ai.complete_code").with_shortcut("Ctrl+Space"),
                MenuItem::new("Spiega codice").with_action("ai.explain_code").with_shortcut("Ctrl+Shift+E"),
                MenuItem::new("Ottimizza codice").with_action("ai.optimize_code"),
                MenuItem::new("Genera test").with_action("ai.generate_tests").with_separator(),
                MenuItem::new("Impostazioni AI").with_action("ai.settings"),
            ]),
            DropdownMenu::new("Aiuto", vec![
                MenuItem::new("Documentazione").with_action("help.docs"),
                MenuItem::new("Scorciatoie").with_action("help.shortcuts").with_separator(),
                MenuItem::new("Informazioni").with_action("help.about"),
            ]),
        ];

        let theme1 = theme.clone();
        let theme2 = theme.clone();
        let theme3 = theme.clone();
        let theme4 = theme.clone();
        let theme5 = theme.clone();
        let theme6 = theme.clone();
        let theme7 = theme.clone();
        let theme8 = theme.clone();
        
        Ok(
            container(
                h_stack((
                    // Logo e titolo (lato sinistro)
                    container(
                        h_stack((
                            container(
                                if show_logo {
                                    h_stack((
                                        // Logo MATRIX
                                        label(|| "MATRIX".to_string())
                                            .style(move |s| {
                                                let theme1_clone = theme1.clone();
                                                s.font_size(16.0)
                                                 .font_weight(Weight::BOLD)
                                                 .color(theme1_clone.colors.accent)
                                                 .margin_right(6.0)
                                            }),
                                        // IDE
                                        label(|| "IDE".to_string())
                                            .style(move |s| {
                                                let theme2_clone = theme2.clone();
                                                s.font_size(16.0)
                                                 .font_weight(Weight::NORMAL)
                                                 .color(theme2_clone.colors.text)
                                            }),
                                    ))
                                    .style(|s| s.margin_right(20.0))
                                } else {
                                    empty()
                                }
                            ),

                            // Menu dropdown
                            h_stack_from_iter(
                                menus.into_iter().map({
                                    let theme = theme.clone();
                                    let active_menu = active_menu.clone();
                                    move |menu| {
                                        create_native_dropdown_menu(menu, theme.clone(), active_menu.clone())
                                    }
                                })
                            )
                            .style(|s| s.gap(4.0)),
                        ))
                    )
                    .style(|s| s.height_full()),

                    // Titolo centrale
                    container(
                        label(move || {
                            if let Some(title) = &custom_title {
                                title.clone()
                            } else {
                                current_title.get()
                            }
                        })
                        .style(move |s| {
                            let theme3_clone = theme3.clone();
                            s.font_size(13.0)
                             .color(theme3_clone.colors.text_secondary)
                        })
                    )
                    .style(|s| {
                        s.flex_grow(1.0)
                         .height_full()
                         .align_items(Some(AlignItems::Center))
                         .justify_content(Some(JustifyContent::Center))
                    }),

                    // Controlli finestra (lato destro)
                    if show_window_controls {
                        h_stack((
                            // Minimize
                            container(
                                label(|| "−".to_string())
                            )
                            .on_click_stop(|_| {
                                println!("Minimize window");
                            })
                            .style(move |s| {
                                let theme4_clone = theme4.clone();
                                let theme5_clone = theme5.clone();
                                let theme6_clone = theme6.clone();
                                let theme7_clone = theme7.clone();
                                s.size(32.0, 32.0)
                                 .font_size(14.0)
                                 .color(theme4_clone.colors.text)
                                 .background(theme5_clone.colors.transparent)
                                 .cursor(CursorStyle::Pointer)
                                 .hover(move |s| {
                                     let theme6_clone2 = theme6_clone.clone();
                                     s.background(theme6_clone2.colors.hover)
                                 })
                                 .active(move |s| {
                                     let theme7_clone2 = theme7_clone.clone();
                                     s.background(theme7_clone2.colors.active)
                                 })
                                 .align_items(Some(AlignItems::Center))
                                 .justify_content(Some(JustifyContent::Center))
                            }),
                    
                            // Maximize/Restore
                            container(
                                label({
                                    let window_maximized = window_maximized.clone();
                                    move || {
                                        if window_maximized.get() { "❐" } else { "□" }.to_string()
                                    }
                                })
                            )
                            .on_click_stop({
                                let window_maximized = window_maximized.clone();
                                move |_| {
                                    let new_state = !window_maximized.get();
                                    window_maximized.set(new_state);
                                    println!("Toggle maximize: {}", new_state);
                                }
                            })
                            .style(move |s| {
                                let theme8_clone = theme8.clone();
                                s.size(32.0, 32.0)
                                 .font_size(12.0)
                                 .color(theme8_clone.colors.text)
                                 .background(theme8_clone.colors.transparent)
                                 .cursor(CursorStyle::Pointer)
                                 .hover(move |s| {
                                     let theme8_clone2 = theme8_clone.clone();
                                     s.background(theme8_clone2.colors.hover)
                                 })
                                 .active(move |s| {
                                     let theme8_clone3 = theme8_clone.clone();
                                     s.background(theme8_clone3.colors.active)
                                 })
                                 .align_items(Some(AlignItems::Center))
                                 .justify_content(Some(JustifyContent::Center))
                            }),
                    
                            // Close
                            container(
                                label(|| "×".to_string())
                            )
                            .on_click_stop(|_| {
                                println!("Close window");
                            })
                            .style(move |s| {
                                let theme8_clone4 = theme8.clone();
                                s.size(32.0, 32.0)
                                 .font_size(16.0)
                                 .color(theme8_clone4.colors.text)
                                 .background(theme8_clone4.colors.transparent)
                                 .cursor(CursorStyle::Pointer)
                                 .hover(move |s| {
                                     let theme8_clone5 = theme8_clone4.clone();
                                     s.background(theme8_clone5.colors.error.multiply_alpha(0.7))
                                 })
                                 .active(move |s| {
                                     let theme8_clone6 = theme8_clone4.clone();
                                     s.background(theme8_clone6.colors.error.multiply_alpha(0.9))
                                 })
                                 .align_items(Some(AlignItems::Center))
                                 .justify_content(Some(JustifyContent::Center))
                            }),
                        ))
                    } else {
                        h_stack((empty(),))
                    },
                ))
                .style(move |s| {
                    let theme8_clone7 = theme8.clone();
                    s.width_full()
                     .height(config_height)
                     .background(theme8_clone7.colors.background_secondary)
                     .border_bottom(1.0)
                     .border_color(theme8_clone7.colors.border)
                     .justify_content(Some(JustifyContent::SpaceBetween))
                     .align_items(Some(AlignItems::Center))
                })
                // Chiude tutti i menu quando si clicca sulla title bar
                .on_click_stop({
                    let active_menu = active_menu.clone();
                    move |_| {
                        active_menu.set(None);
                    }
                })
            )
        )
    }

}

/// Crea un menu dropdown utilizzando l'API nativa di Floem 0.2
fn create_native_dropdown_menu(menu: DropdownMenu, theme: Theme, active_menu: RwSignal<Option<String>>) -> impl View {
    let menu_title = menu.title.clone();
    let menu_items = menu.items.clone();

    // Estraiamo solo le etichette per il dropdown
    let menu_labels: Vec<String> = menu_items.iter().map(|item| item.label.clone()).collect();

    // Segnale per l'elemento selezionato - inizializziamo con il primo elemento se disponibile
    let selected_item = create_rw_signal(
        menu_labels.first().cloned().unwrap_or_else(|| menu_title.clone())
    );

    // Creiamo un dropdown utilizzando l'API nativa di Floem 0.2
    dropdown::Dropdown::new_rw(selected_item, menu_labels.into_iter())
        .on_accept({
            move |selected_label| {
                // Trova l'elemento corrispondente e esegui l'azione
                if let Some(menu_item) = menu_items.iter().find(|item| item.label == selected_label) {
                    if let Some(action) = &menu_item.action {
                        execute_menu_action(action);
                    }
                }
                println!("Selected menu item: {}", selected_label);
            }
        })
        .style(|s| s.margin_right(2.0))
}

/// Funzione helper per creare una title bar con configurazione di default
pub fn matrix_title_bar(theme_manager: Arc<ThemeManager>) -> Result<impl View, UiError> {
    let title_bar = TitleBar::new(theme_manager)?;
    title_bar.build()
}

/// Funzione helper per creare una title bar con configurazione personalizzata
pub fn matrix_title_bar_with_config(
    theme_manager: Arc<ThemeManager>,
    config: TitleBarConfig,
) -> Result<impl View, UiError> {
    let title_bar = TitleBar::with_config(theme_manager, config)?;
    title_bar.build()
}

/// Esegue un'azione del menu
fn execute_menu_action(action: &str) {
    println!("🔍 DEBUG: Esecuzione azione menu: {}", action);

    // Ottieni lo stato globale
    if let Some(state) = GlobalAppState::get() {
        match action {
            // File menu actions
            "file.new" => {
                println!("Creating new file...");
                // Implementazione: Creare un nuovo file
            }
            "file.open" => {
                println!("Opening file...");
                // Implementazione: Aprire un file esistente
            }
            "file.save" => {
                println!("Saving file...");
                // Implementazione: Salvare il file corrente
            }
            "file.save_as" => {
                println!("Saving file as...");
                // Implementazione: Salvare il file corrente con un nuovo nome
            }
            "file.preferences" => {
                println!("Opening preferences...");
                // Implementazione: Aprire le preferenze
            }
            "file.exit" => {
                println!("Exiting application...");
                // Implementazione: Uscire dall'applicazione
            }

            // Edit menu actions
            "edit.undo" => {
                println!("Undo...");
                // Implementazione: Annullare l'ultima azione
            }
            "edit.redo" => {
                println!("Redo...");
                // Implementazione: Ripetere l'ultima azione annullata
            }
            "edit.cut" => {
                println!("Cut...");
                // Implementazione: Tagliare il testo selezionato
            }
            "edit.copy" => {
                println!("Copy...");
                // Implementazione: Copiare il testo selezionato
            }
            "edit.paste" => {
                println!("Paste...");
                // Implementazione: Incollare il testo dagli appunti
            }
            "edit.find" => {
                println!("Find...");
                // Implementazione: Aprire la finestra di ricerca
            }
            "edit.replace" => {
                println!("Replace...");
                // Implementazione: Aprire la finestra di sostituzione
            }

            // View menu actions
            "view.file_panel" => {
                println!("Toggling file panel...");
                // Implementazione: Mostrare/nascondere il pannello file
            }
            "view.terminal_panel" => {
                println!("Toggling terminal panel...");
                // Implementazione: Mostrare/nascondere il pannello terminale
            }
            "view.output_panel" => {
                println!("Toggling output panel...");
                // Implementazione: Mostrare/nascondere il pannello output
            }
            "view.zoom_in" => {
                println!("Zoom in...");
                // Implementazione: Aumentare lo zoom
            }
            "view.zoom_out" => {
                println!("Zoom out...");
                // Implementazione: Diminuire lo zoom
            }
            "view.zoom_reset" => {
                println!("Reset zoom...");
                // Implementazione: Reimpostare lo zoom
            }

            // Project menu actions
            "project.open" => {
                println!("Opening project...");
                // Implementazione: Aprire un progetto
            }
            "project.close" => {
                println!("Closing project...");
                // Implementazione: Chiudere il progetto corrente
            }
            "project.run" => {
                println!("Running project...");
                // Implementazione: Eseguire il progetto
            }
            "project.build" => {
                println!("Building project...");
                // Implementazione: Compilare il progetto
            }
            "project.clean" => {
                println!("Cleaning project...");
                // Implementazione: Pulire il progetto
            }

            // AI menu actions
            "ai.complete_code" => {
                println!("Completing code...");
                // Implementazione: Completamento codice con AI
            }
            "ai.explain_code" => {
                println!("Explaining code...");
                // Implementazione: Spiegazione codice con AI
            }
            "ai.optimize_code" => {
                println!("Optimizing code...");
                // Implementazione: Ottimizzazione codice con AI
            }
            "ai.generate_tests" => {
                println!("Generating tests...");
                // Implementazione: Generazione test con AI
            }
            "ai.settings" => {
                println!("Opening AI settings...");
                // Implementazione: Impostazioni AI
            }

            // Help menu actions
            "help.docs" => {
                println!("Opening documentation...");
                // Implementazione: Aprire la documentazione
            }
            "help.shortcuts" => {
                println!("Opening shortcuts...");
                // Implementazione: Aprire la lista delle scorciatoie
            }
            "help.about" => {
                println!("Opening about dialog...");
                // Implementazione: Aprire la finestra di informazioni
            }

            // Azioni non riconosciute
            _ => {
                println!("Unknown action: {}", action);
            }
        }
    } else {
        println!("⚠️ WARNING: GlobalAppState not initialized");
    }
}
