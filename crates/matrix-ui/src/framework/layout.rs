//! Professional Layout System for MATRIX_IDE
//!
//! This module provides a comprehensive layout system that creates
//! professional IDE layouts with resizable panels and proper splitters.

use std::sync::Arc;
use floem::{
    View, 
    reactive::{RwSignal, SignalGet, SignalUpdate},
    views::{v_stack, h_stack, container, stack, empty, Decorators},
    style::{Position, CursorStyle},
    event::{Event, EventListener, EventPropagation},
};
use crate::{
    error::UiError,
    theme::{Theme, ThemeManager},
    framework::{
        component::{ProfessionalComponent, ComponentPosition, ComponentSize},
        state::StateManager,
    },
    components::file_explorer_improved::FileExplorer,
    panels::ai_panel::AiPanel,
};
use matrix_core;

/// Professional layout manager for MATRIX_IDE
pub struct ProfessionalLayoutManager {
    theme_manager: Arc<ThemeManager>,
    state_manager: Arc<StateManager>,
    layout_config: RwSignal<LayoutConfig>,
    splitter_system: Arc<SplitterSystem>,
    file_explorer: Arc<FileExplorer>,
    ai_panel: Arc<AiPanel>,
}

impl ProfessionalLayoutManager {
    /// Creates a new professional layout manager
    pub fn new(
        theme_manager: Arc<ThemeManager>,
        state_manager: Arc<StateManager>,
    ) -> Result<Self, UiError> {
        let layout_config = RwSignal::new(LayoutConfig::default());
        let splitter_system = Arc::new(SplitterSystem::new(theme_manager.clone()));

        // Create file explorer
        let file_explorer = Arc::new(FileExplorer::new(theme_manager.clone()));

        // Create AI panel
        let ai_panel = Arc::new(AiPanel::new(
            Arc::new(matrix_core::Engine::new().map_err(|e| UiError::CoreError(e.to_string()))?),
            theme_manager.clone(),
        )?);

        Ok(Self {
            theme_manager,
            state_manager,
            layout_config,
            splitter_system,
            file_explorer,
            ai_panel,
        })
    }

    /// Creates the main application layout - this is called from App::run()
    pub fn create_main_layout(&self) -> Result<impl View, UiError> {
        // Delegate to the existing build_professional_layout method
        self.build_professional_layout()
    }

    /// Builds the complete professional layout
    pub fn build_professional_layout(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        // Create layout signals for resizable panels
        let left_panel_width = RwSignal::new(280.0);
        let right_panel_width = RwSignal::new(320.0);
        let bottom_panel_height = RwSignal::new(240.0);
        
        // Create the main layout structure
        let main_layout = v_stack((
            self.create_title_bar()?,
            self.create_main_content_area(
                left_panel_width,
                right_panel_width,
                bottom_panel_height,
            )?,
            self.create_status_bar()?,
        ))
        .style(move |s| {
            s.size_full()
                .background(theme.colors.background)
        });

        Ok(main_layout)
    }

    /// Creates the title bar with menu and controls
    fn create_title_bar(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let title_bar = h_stack((
            // Application title and menu
            container(floem::views::label(|| "MATRIX_IDE".to_string()))
                .style(move |s| {
                    s.font_bold()
                        .font_size(theme.font_sizes.lg)
                        .font_family(theme.typography.font_family_ui.clone())
                        .color(theme.colors.text)
                        .padding_horiz(theme.spacing.md)
                }),

            // Window controls placeholder
            container(empty())
                .style(|s| s.width(100.0)),
        ))
        .style(move |s| {
            s.height(32.0)
                .width_full()
                .background(theme.colors.background_secondary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
                .justify_content(floem::style::JustifyContent::SpaceBetween)
                .align_items(floem::style::AlignItems::Center)
        });

        Ok(title_bar)
    }

    /// Creates the main content area with resizable panels
    fn create_main_content_area(
        &self,
        left_panel_width: RwSignal<f64>,
        right_panel_width: RwSignal<f64>,
        bottom_panel_height: RwSignal<f64>,
    ) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        // Create splitter drag states
        let is_left_dragging = RwSignal::new(false);
        let is_right_dragging = RwSignal::new(false);
        let is_bottom_dragging = RwSignal::new(false);

        // Left sidebar panel
        let left_panel = self.create_sidebar_panel(
            PanelPosition::Left,
            left_panel_width,
        )?;

        // Right sidebar panel
        let right_panel = self.create_sidebar_panel(
            PanelPosition::Right,
            right_panel_width,
        )?;

        // Center area with editor and bottom panel
        let center_area = v_stack((
            self.create_editor_area()?,
            self.create_bottom_panel(bottom_panel_height)?,
        ))
        .style(|s| s.flex_grow(1.0).height_full());

        // Create splitters
        let left_splitter = self.splitter_system.create_vertical_splitter(
            left_panel_width,
            is_left_dragging,
            SplitterPosition::Left,
        );

        let right_splitter = self.splitter_system.create_vertical_splitter(
            right_panel_width,
            is_right_dragging,
            SplitterPosition::Right,
        );

        let bottom_splitter = self.splitter_system.create_horizontal_splitter(
            bottom_panel_height,
            is_bottom_dragging,
            left_panel_width,
            right_panel_width,
        );

        // Main content with overlay splitters
        let main_content = stack((
            h_stack((
                left_panel,
                center_area,
                right_panel,
            ))
            .style(|s| s.flex_grow(1.0).width_full()),
            
            // Overlay splitters
            left_splitter,
            right_splitter,
            bottom_splitter,
        ))
        .on_event(EventListener::PointerMove, move |event| {
            if let Event::PointerMove(pointer_event) = event {
                let pos = pointer_event.pos;
                
                // Handle left panel resizing
                if is_left_dragging.get() {
                    let new_width = pos.x.max(200.0).min(500.0);
                    left_panel_width.set(new_width);
                }
                
                // Handle right panel resizing
                if is_right_dragging.get() {
                    // Calculate from right edge (assuming window width of 1200)
                    let window_width = 1200.0; // TODO: Get actual window width
                    let new_width = (window_width - pos.x).max(200.0).min(500.0);
                    right_panel_width.set(new_width);
                }
                
                // Handle bottom panel resizing
                if is_bottom_dragging.get() {
                    // Calculate from bottom edge (assuming window height of 800)
                    let window_height = 800.0; // TODO: Get actual window height
                    let new_height = (window_height - pos.y - 32.0 - 24.0).max(100.0).min(400.0); // Account for title bar and status bar
                    bottom_panel_height.set(new_height);
                }
            }
            EventPropagation::Continue
        })
        .on_event(EventListener::PointerUp, move |_| {
            is_left_dragging.set(false);
            is_right_dragging.set(false);
            is_bottom_dragging.set(false);
            EventPropagation::Continue
        });

        Ok(main_content)
    }

    /// Creates a sidebar panel container
    fn create_sidebar_panel(
        &self,
        position: PanelPosition,
        width_signal: RwSignal<f64>,
    ) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let panel = container(
            v_stack((
                // Panel header with tabs
                self.create_panel_header(&position)?,
                // Panel content
                self.create_panel_content(&position)?,
            ))
        )
        .style(move |s| {
            s.width(width_signal.get())
                .height_full()
                .background(theme.colors.background_secondary)
                .border_color(theme.colors.border)
                .apply_if(position == PanelPosition::Left, |s| s.border_right(1.0))
                .apply_if(position == PanelPosition::Right, |s| s.border_left(1.0))
        });

        Ok(panel)
    }

    /// Creates a panel header with tabs
    fn create_panel_header(&self, position: &PanelPosition) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let tabs = match position {
            PanelPosition::Left => vec!["Files", "Search", "Git", "Extensions"],
            PanelPosition::Right => vec!["AI", "Properties", "Outline", "Plugins"],
            _ => vec!["Tab1", "Tab2"],
        };

        let tab_views: Vec<_> = tabs.into_iter().map(|tab_name| {
            let name = tab_name.to_string();
            container(floem::views::label(move || name.clone()))
                .style(move |s| {
                    s.padding_horiz(12.0)
                        .padding_vert(8.0)
                        .color(theme.colors.text_secondary)
                        .font_size(12.0)
                        .hover(|s| s.background(theme.colors.hover))
                        .cursor(CursorStyle::Pointer)
                })
        }).collect();

        let tab_bar = container(
            floem::views::h_stack_from_iter(tab_views)
        )
        .style(move |s| {
            s.height(32.0)
                .width_full()
                .background(theme.colors.background_tertiary)
                .border_bottom(1.0)
                .border_color(theme.colors.border)
        });

        Ok(tab_bar)
    }

    /// Creates panel content based on position
    fn create_panel_content(&self, position: &PanelPosition) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let content = match position {
            PanelPosition::Left => {
                // Real file explorer
                container(self.file_explorer.build())
            }
            PanelPosition::Right => {
                // Real AI panel
                container(self.ai_panel.build())
            }
            _ => {
                container(floem::views::label(|| "Panel Content".to_string()))
            }
        }
        .style(move |s| {
            s.flex_grow(1.0)
                .width_full()
                .padding(12.0)
                .color(theme.colors.text)
                .font_size(13.0)
        });

        Ok(content)
    }

    /// Creates the editor area
    fn create_editor_area(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let editor = container(
            v_stack((
                // Editor tab bar
                h_stack((
                    container(floem::views::label(|| "main.rs".to_string()))
                        .style(move |s| {
                            s.padding_horiz(12.0)
                                .padding_vert(8.0)
                                .background(theme.colors.background)
                                .color(theme.colors.text)
                                .font_size(13.0)
                                .border_right(1.0)
                                .border_color(theme.colors.border)
                        }),
                ))
                .style(move |s| {
                    s.height(32.0)
                        .width_full()
                        .background(theme.colors.background_secondary)
                        .border_bottom(1.0)
                        .border_color(theme.colors.border)
                }),
                
                // Editor content
                container(floem::views::label(|| "// Welcome to MATRIX_IDE\n\nfn main() {\n    println!(\"Hello, MATRIX!\");\n}".to_string()))
                    .style(move |s| {
                        s.flex_grow(1.0)
                            .width_full()
                            .padding(theme.spacing.lg)
                            .color(theme.colors.text)
                            .font_family(theme.typography.font_family_code.clone())
                            .font_size(theme.font_sizes.md)
                            .line_height(theme.typography.line_height_normal)
                    }),
            ))
        )
        .style(move |s| {
            s.flex_grow(1.0)
                .width_full()
                .background(theme.colors.background)
        });

        Ok(editor)
    }

    /// Creates the bottom panel
    fn create_bottom_panel(&self, height_signal: RwSignal<f64>) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let panel = container(
            v_stack((
                // Bottom panel tabs
                h_stack((
                    container(floem::views::label(|| "Terminal".to_string()))
                        .style(move |s| {
                            s.padding_horiz(12.0)
                                .padding_vert(8.0)
                                .color(theme.colors.text)
                                .font_size(12.0)
                                .background(theme.colors.background_tertiary)
                        }),
                    container(floem::views::label(|| "Output".to_string()))
                        .style(move |s| {
                            s.padding_horiz(12.0)
                                .padding_vert(8.0)
                                .color(theme.colors.text_secondary)
                                .font_size(12.0)
                                .hover(|s| s.background(theme.colors.hover))
                                .cursor(CursorStyle::Pointer)
                        }),
                    container(floem::views::label(|| "Problems".to_string()))
                        .style(move |s| {
                            s.padding_horiz(12.0)
                                .padding_vert(8.0)
                                .color(theme.colors.text_secondary)
                                .font_size(12.0)
                                .hover(|s| s.background(theme.colors.hover))
                                .cursor(CursorStyle::Pointer)
                        }),
                ))
                .style(move |s| {
                    s.height(32.0)
                        .width_full()
                        .background(theme.colors.background_secondary)
                        .border_bottom(1.0)
                        .border_color(theme.colors.border)
                }),
                
                // Terminal content
                container(floem::views::label(|| "$ cargo run\n   Compiling matrix-ui v0.1.0\n   Finished dev [unoptimized + debuginfo] target(s)\n$ ".to_string()))
                    .style(move |s| {
                        s.flex_grow(1.0)
                            .width_full()
                            .padding(12.0)
                            .color(theme.colors.text)
                            .font_family("JetBrains Mono, Consolas, monospace".to_string())
                            .font_size(13.0)
                            .line_height(1.4)
                    }),
            ))
        )
        .style(move |s| {
            s.height(height_signal.get())
                .width_full()
                .background(theme.colors.background_secondary)
                .border_top(1.0)
                .border_color(theme.colors.border)
        });

        Ok(panel)
    }

    /// Creates the status bar
    fn create_status_bar(&self) -> Result<impl View, UiError> {
        let theme = self.theme_manager.get_active_theme()?;
        
        let status_bar = h_stack((
            // Left side status items
            h_stack((
                container(floem::views::label(|| "Ready".to_string()))
                    .style(move |s| {
                        s.padding_horiz(8.0)
                            .color(theme.colors.text_secondary)
                            .font_size(12.0)
                    }),
            )),
            
            // Right side status items
            h_stack((
                container(floem::views::label(|| "Rust".to_string()))
                    .style(move |s| {
                        s.padding_horiz(8.0)
                            .color(theme.colors.text_secondary)
                            .font_size(12.0)
                    }),
                container(floem::views::label(|| "Ln 1, Col 1".to_string()))
                    .style(move |s| {
                        s.padding_horiz(8.0)
                            .color(theme.colors.text_secondary)
                            .font_size(12.0)
                    }),
            )),
        ))
        .style(move |s| {
            s.height(24.0)
                .width_full()
                .background(theme.colors.background_secondary)
                .border_top(1.0)
                .border_color(theme.colors.border)
                .justify_content(floem::style::JustifyContent::SpaceBetween)
                .align_items(floem::style::AlignItems::Center)
        });

        Ok(status_bar)
    }
}

/// Layout configuration
#[derive(Debug, Clone)]
pub struct LayoutConfig {
    pub left_panel_width: f64,
    pub right_panel_width: f64,
    pub bottom_panel_height: f64,
    pub title_bar_height: f64,
    pub status_bar_height: f64,
    pub splitter_width: f64,
}

impl Default for LayoutConfig {
    fn default() -> Self {
        Self {
            left_panel_width: 280.0,
            right_panel_width: 320.0,
            bottom_panel_height: 240.0,
            title_bar_height: 32.0,
            status_bar_height: 24.0,
            splitter_width: 4.0,
        }
    }
}

/// Panel positions
#[derive(Debug, Clone, PartialEq)]
pub enum PanelPosition {
    Left,
    Right,
    Bottom,
    Center,
}

/// Splitter system for resizable panels
pub struct SplitterSystem {
    theme_manager: Arc<ThemeManager>,
}

impl SplitterSystem {
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        Self { theme_manager }
    }

    /// Creates a vertical splitter
    pub fn create_vertical_splitter(
        &self,
        panel_width: RwSignal<f64>,
        is_dragging: RwSignal<bool>,
        position: SplitterPosition,
    ) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(empty())
            .style(move |s| {
                let left_pos = match position {
                    SplitterPosition::Left => panel_width.get(),
                    SplitterPosition::Right => 0.0, // Will be calculated from right
                    _ => 0.0,
                };
                
                s.position(Position::Absolute)
                    .z_index(10)
                    .inset_top(32.0) // Account for title bar
                    .inset_bottom(24.0) // Account for status bar
                    .width(4.0)
                    .cursor(CursorStyle::ColResize)
                    .apply_if(position == SplitterPosition::Left, |s| s.inset_left(left_pos))
                    .apply_if(position == SplitterPosition::Right, |s| s.inset_right(panel_width.get()))
                    .hover(|s| s.background(theme.colors.accent))
                    .apply_if(is_dragging.get(), |s| s.background(theme.colors.accent))
            })
            .on_event(EventListener::PointerDown, move |_| {
                is_dragging.set(true);
                EventPropagation::Continue
            })
    }

    /// Creates a horizontal splitter
    pub fn create_horizontal_splitter(
        &self,
        panel_height: RwSignal<f64>,
        is_dragging: RwSignal<bool>,
        left_panel_width: RwSignal<f64>,
        right_panel_width: RwSignal<f64>,
    ) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        
        container(empty())
            .style(move |s| {
                s.position(Position::Absolute)
                    .z_index(10)
                    .inset_left(left_panel_width.get() + 4.0)
                    .inset_right(right_panel_width.get() + 4.0)
                    .inset_bottom(panel_height.get() + 24.0) // Account for status bar
                    .height(4.0)
                    .cursor(CursorStyle::RowResize)
                    .hover(|s| s.background(theme.colors.accent))
                    .apply_if(is_dragging.get(), |s| s.background(theme.colors.accent))
            })
            .on_event(EventListener::PointerDown, move |_| {
                is_dragging.set(true);
                EventPropagation::Continue
            })
    }
}

/// Splitter positions
#[derive(Debug, Clone, PartialEq)]
pub enum SplitterPosition {
    Left,
    Right,
    Bottom,
}
