[package]
name = "socrate"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# da KiloCode (git submodule o git dep con rev specifico)
# kilocode = { git = "https://github.com/Kilo-Org/kilocode", package = "kilocode", rev = "main", default-features = false }

# da Cline (solo mod agent_loop / eventuali utils)
# cline-core = { git = "https://github.com/ericmjp/cline", package = "cline-core", rev = "main", default-features = false }

anyhow = "1"
async-trait = "0.1"
tokio = { version = "1", features = ["rt-multi-thread", "macros", "sync"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
once_cell = "1"
toml = "0.8"
