use socrate::config::{AgentConfig, MpcConfig};
use socrate::mpc::{MpcRegistry, MPCModule};
use socrate::mpc::context7::Context7;
use socrate::mpc::memory_graph::MemoryGraph;
use socrate::mpc::sequential::Sequential;
use std::sync::Arc;

#[tokio::test]
async fn test_mpc_registry() {
    let mut registry = MpcRegistry::new();
    registry.register("context7", Arc::new(Context7));
    registry.register("memory_graph", Arc::new(MemoryGraph));
    registry.register("sequential", Arc::new(Sequential));

    let config = AgentConfig {
        mpc: vec![
            MpcConfig {
                name: "context7".to_string(),
                enabled: true,
            },
            MpcConfig {
                name: "memory_graph".to_string(),
                enabled: true,
            },
            MpcConfig {
                name: "sequential".to_string(),
                enabled: false, // This one is disabled
            },
        ],
    };

    // Simulate initializing MPC modules based on config
    for mpc_config in config.mpc {
        if mpc_config.enabled {
            if let Some(module) = registry.modules.get(&mpc_config.name) {
                module.init(&mpc_config).await.unwrap();
            }
        }
    }
}
