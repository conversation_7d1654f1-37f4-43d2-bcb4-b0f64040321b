use socrate::agent::agent_loop;
use socrate::tools;
use socrate::tools::fs::{ExecuteCmdTool, WriteFileTool};
use socrate::tools::search::SearchTool;

#[tokio::test(flavor = "multi_thread")]
async fn smoke_test() {
    // Register dummy tools
    tools::register("write_file", WriteFileTool).await;
    tools::register("execute_cmd", ExecuteCmdTool).await;
    tools::register("search", SearchTool).await;

    // Run the demo loop
    let result = agent_loop::run("Genera un file hello.rs, compilalo e prova il binario".to_string()).await;
    assert!(result.is_ok());

    // Verify that the file was "created"
    // In a real scenario, we would check the file system.
    // For now, we'll just check if the test completes.
}
