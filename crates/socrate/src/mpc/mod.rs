pub mod context7;
pub mod memory_graph;
pub mod sequential;

use crate::config::MpcConfig;
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;

#[async_trait]
pub trait MPCModule: Send + Sync {
    async fn init(&self, cfg: &MpcConfig) -> anyhow::Result<()>;
}

pub struct MpcRegistry {
    modules: HashMap<String, Arc<dyn MPCModule>>,
}

impl MpcRegistry {
    pub fn new() -> Self {
        Self {
            modules: HashMap::new(),
        }
    }

    pub fn register(&mut self, name: &str, module: Arc<dyn MPCModule>) {
        self.modules.insert(name.to_string(), module);
    }
}
