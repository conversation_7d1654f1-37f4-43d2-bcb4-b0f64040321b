use crate::planner::Planner;
use crate::tools;
use anyhow::Result;

// A dummy planner for the demo loop
struct DummyPlanner;

#[async_trait::async_trait]
impl Planner for DummyPlanner {
    async fn plan(&self, goal: String) -> Result<Vec<crate::task::Task>> {
        println!("Planning for goal: {}", goal);
        // For the demo, we'll just create a dummy task
        Ok(vec![crate::task::Task {
            id: 1,
            kind: crate::task::TaskKind::ExecuteCmd {
                cmd: "echo 'hello'".to_string(),
            },
            status: crate::task::TaskStatus::Pending,
        }])
    }
}

pub async fn run(goal: String) -> Result<()> {
    let planner = DummyPlanner;
    let tasks = planner.plan(goal).await?;
    for t in tasks {
        tools::execute(&t).await?;
    }
    Ok(())
}
