use crate::planner::kilo_port::<PERSON><PERSON><PERSON>lanner;
use crate::planner::Planner;
use crate::task::{Task, TaskStatus};
use crate::tools;
use anyhow::Result;
use std::collections::VecDeque;
use tokio::sync::RwLock;
use std::sync::Arc;

pub struct AgentLoop {
    planner: Arc<dyn Planner + Send + Sync>,
    task_queue: Arc<RwLock<VecDeque<Task>>>,
}

impl AgentLoop {
    pub fn new(planner: Arc<dyn Planner + Send + Sync>) -> Self {
        Self {
            planner,
            task_queue: Arc::new(RwLock::new(VecDeque::new())),
        }
    }

    pub async fn run(&self, goal: String) -> Result<()> {
        let tasks = self.planner.plan(goal).await?;
        for task in tasks {
            self.task_queue.write().await.push_back(task);
        }

        while let Some(mut task) = self.task_queue.write().await.pop_front() {
            println!("Executing task: {}", task);
            task.status = TaskStatus::Running;
            let result = tools::execute(&task).await;
            task.status = result.unwrap_or_else(|e| TaskStatus::Failed(e.to_string()));
            println!("Task finished with status: {:?}", task.status);
            // Here we would interact with matrix-fabric
        }

        Ok(())
    }
}

// This is the entry point that the test calls
pub async fn run(goal: String) -> Result<()> {
    let planner = Arc::new(KiloPlanner);
    let agent_loop = AgentLoop::new(planner);
    agent_loop.run(goal).await
}
