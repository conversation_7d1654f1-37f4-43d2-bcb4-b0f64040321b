pub mod ast;
pub mod code_exec;
pub mod fs;
pub mod git;
pub mod search;

use crate::task::{Task, TaskKind, TaskStatus};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[async_trait::async_trait]
pub trait Tool: Send + Sync {
    async fn execute(&self, task: &Task) -> anyhow::Result<TaskStatus>;
}

static REGISTRY: once_cell::sync::Lazy<RwLock<HashMap<&'static str, Arc<dyn Tool>>>> =
    once_cell::sync::Lazy::new(|| RwLock::new(HashMap::new()));

pub async fn register<T: Tool + 'static>(name: &'static str, tool: T) {
    REGISTRY.write().await.insert(name, Arc::new(tool));
}

pub async fn execute(task: &Task) -> anyhow::Result<TaskStatus> {
    // semplice dispatch demo
    let registry = REGISTRY.read().await;
    let tool_name = match &task.kind {
        TaskKind::WriteFile { .. } => "write_file",
        TaskKind::ExecuteCmd { .. } => "execute_cmd",
        TaskKind::Search { .. } => "search",
    };

    let exec_tool = registry
        .get(tool_name)
        .ok_or_else(|| anyhow::anyhow!("Tool not found: {}", tool_name))?;
    exec_tool.execute(task).await
}
